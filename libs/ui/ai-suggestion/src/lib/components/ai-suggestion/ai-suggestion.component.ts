import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  Renderer2,
  booleanAttribute,
  forwardRef,
  numberAttribute,
} from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import {
  BehaviorSubject,
  Observable,
  combineLatest,
  filter,
  finalize,
  interval,
  map,
  merge,
  scan,
  startWith,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { AiSuggestionState } from '../../models/ai-suggestion-state';

/**
 * Reusable AI suggestion component that provides animated typewriter overlay functionality
 * for form controls. Shows AI suggestions with character-by-character typewriter effect on value changes.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-ai-suggestion--docs Storybook Reference}
 */
@Component({
  selector: 'fin-ai-suggestion',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './ai-suggestion.component.html',
  styleUrl: './ai-suggestion.component.scss',
  host: {
    class: 'fin-ai-suggestion',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinAiSuggestionComponent),
      multi: true,
    },
  ],
})
export class FinAiSuggestionComponent
  extends FinControlValueAccessor
  implements OnInit, OnChanges
{
  /**
   * Enables AI suggestion animations. When true, suggestions appear on input changes with typewriter effect.
   * Animation only triggers when BOTH `aiEnabled` is true AND input value changes.
   * @default false
   */
  @Input({ transform: booleanAttribute }) aiEnabled = false;

  /**
   * Speed of the typewriter effect in milliseconds per character.
   * Lower values make the typewriter effect faster.
   */
  @Input({ transform: numberAttribute }) typewriterSpeed = 100;

  /**
   * Emitted when the AI suggestion typewriter animation completes.
   * Only emits when the typewriter animation completes naturally, not when aiEnabled is disabled.
   */
  @Output() aiSuggestionReady = new EventEmitter<void>();

  /** Internal BehaviorSubject to manage aiEnabled state reactively */
  private aiEnabledSubject$$ = new BehaviorSubject<boolean>(false);

  /** Internal BehaviorSubject to manage typewriter text progression and visibility */
  private typewriterText$$ = new BehaviorSubject<string>('');

  /**
   * Observable that emits the progressively revealed typewriter text.
   * Emits empty string when no animation should be visible, eliminating need for separate visibility logic.
   */
  protected typewriterText$ = this.typewriterText$$.asObservable();

  /** Tracks `aiEnabled` input state for reactive state management. Combined with valueChanges to trigger animations. */
  protected aiEnabled$ = this.aiEnabledSubject$$.asObservable();

  protected state: AiSuggestionState = {
    text: '',
    enabled: false,
    wholeText: '',
  };

  protected state$ = new Observable<AiSuggestionState>();

  constructor(
    injector: Injector,
    private elementRef: ElementRef,
    private renderer: Renderer2,
  ) {
    super(injector);
  }

  ngOnInit(): void {
    // The state setup here because this.control is not available before ngOnInit
    this.state$ = this.setUpState();

    this.aiEnabledSubject$$.next(this.aiEnabled);
  }

  ngOnChanges(): void {
    this.aiEnabledSubject$$.next(this.aiEnabled);
  }

  private setUpState() {
    return merge(
      this.typewriterText$.pipe(map((text) => ({ text }))),
      this.setupAiSuggestionAnimation().pipe(map((text) => ({ text }))),
      this.aiEnabled$.pipe(map((enabled) => ({ enabled }))),
      this.control.valueChanges.pipe(map((wholeText) => ({ wholeText }))),
    ).pipe(
      scan(
        (state, command) => (
          this.handleCommand(state, command),
          this.handleHostClasses(this.renderer, this.elementRef)(state),
          state
        ),
        this.state,
      ),
    );
  }
  private handleCommand(
    state: AiSuggestionState,
    command: Partial<AiSuggestionState>,
  ): void {
    Object.assign(state, command);
  }

  /**
   * Sets up the AI suggestion typewriter animation logic that triggers when both
   * aiEnabled is true AND the form control value changes
   */
  private setupAiSuggestionAnimation() {
    return combineLatest([
      this.aiEnabledSubject$$,
      this.control.valueChanges.pipe(startWith(this.control.value)),
    ]).pipe(
      filter(([aiEnabled]: [boolean, string | null]) => aiEnabled === true),
      // Switch to typewriter animation for each new value
      switchMap(([, value]: [boolean, string | null]) => {
        // Handle edge cases: empty, null, or undefined values
        const textToType = value?.toString() || '';

        if (textToType.length === 0) {
          // For empty values, emit empty string and complete immediately
          this.typewriterText$$.next('');
          return interval(1).pipe(
            take(1),
            map(() => ''),
          );
        }

        return interval(this.typewriterSpeed).pipe(
          take(textToType.length),
          map((index: number) => textToType.substring(0, index + 1)),
          tap((partialText: string) => this.typewriterText$$.next(partialText)),
          finalize(() => {
            this.typewriterText$$.next('');

            this.aiSuggestionReady.emit();
          }),
        );
      }),
    );
  }

  /**
   * A class is added when there's AI text to display and removed when the whole AI text is displayed or when AI is not enabled.
   */
  private handleHostClasses(
    renderer: Renderer2,
    elementRef: ElementRef,
  ): (state: AiSuggestionState) => void {
    return (state: AiSuggestionState) => {
      if (state.text !== state.wholeText) {
        renderer.addClass(elementRef.nativeElement, 'fin-ai-suggestion-active');
      } else if (state.text == state.wholeText || !state.enabled) {
        renderer.removeClass(
          elementRef.nativeElement,
          'fin-ai-suggestion-active',
        );
      }
    };
  }
}
