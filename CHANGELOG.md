## 0.0.665 (2025-07-30)

This was a version bump only, there were no code changes.

## 0.0.664 (2025-07-29)

This was a version bump only, there were no code changes.

## 0.0.663 (2025-07-29)

This was a version bump only, there were no code changes.

## 0.0.662 (2025-07-28)

This was a version bump only, there were no code changes.

## 0.0.661 (2025-07-28)


### 🚀 Features

- **ai-suggestion.component.html:** remove async pipe and state display logic

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.660 (2025-07-28)


### 🚀 Features

- **ai-suggestion.component.ts:** decrease default typewriterSpeed from 100ms to 50ms per character

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.659 (2025-07-24)

This was a version bump only, there were no code changes.

## 0.0.658 (2025-07-24)

This was a version bump only, there were no code changes.

## 0.0.657 (2025-07-24)

This was a version bump only, there were no code changes.

## 0.0.656 (2025-07-23)

This was a version bump only, there were no code changes.

## 0.0.655 (2025-07-23)


### 🚀 Features

- **truncate-text.directive.ts:** add 'context' and 'maxWidth' properties to inputs array

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.654 (2025-07-23)

This was a version bump only, there were no code changes.

## 0.0.653 (2025-07-23)


### 🚀 Features

- **ui:** add AI suggestion feature to date-picker and autocomplete components

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.652 (2025-07-23)

This was a version bump only, there were no code changes.

## 0.0.651 (2025-07-23)

This was a version bump only, there were no code changes.

## 0.0.650 (2025-07-22)


### 🚀 Features

- **ui-input:** add AI suggestions feature with reactive state management

### ❤️  Thank You

- Ivaylo Cherkezov

## 0.0.649 (2025-07-21)

This was a version bump only, there were no code changes.

## 0.0.648 (2025-07-19)


### 🚀 Features

- add CLAUDE.md with comprehensive project guidelines and commands for Fincloud UI library
- **document-classification:** implement dynamic loading message width calculation
- **ui:** add new document-classification-loading component

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.647 (2025-07-16)

This was a version bump only, there were no code changes.

## 0.0.646 (2025-07-14)

This was a version bump only, there were no code changes.

## 0.0.645 (2025-07-10)


### 🩹 Fixes

- **field-messages:** update message type in template when set

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.644 (2025-07-09)

This was a version bump only, there were no code changes.

## 0.0.643 (2025-07-08)


### 🩹 Fixes

- replace 'disabled' with 'expandDisabled' for consistency

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.642 (2025-07-08)

This was a version bump only, there were no code changes.

## 0.0.641 (2025-07-08)


### 🩹 Fixes

- rename disabled state to expandDisabled for clarity and consistency

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.640 (2025-07-08)

This was a version bump only, there were no code changes.

## 0.0.639 (2025-07-08)

This was a version bump only, there were no code changes.

## 0.0.638 (2025-07-07)


### 🩹 Fixes

- add missing period to loading timeout message

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.637 (2025-07-07)

This was a version bump only, there were no code changes.

## 0.0.636 (2025-07-07)

This was a version bump only, there were no code changes.

## 0.0.635 (2025-07-04)

This was a version bump only, there were no code changes.

## 0.0.634 (2025-07-04)

This was a version bump only, there were no code changes.

## 0.0.633 (2025-07-04)

This was a version bump only, there were no code changes.

## 0.0.632 (2025-07-03)


### 🩹 Fixes

- **document-classification:** add optional chaining to prevent errors when closing accordion
- **document-classification:** simplify loading state condition in component logic

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.631 (2025-07-02)


### 🚀 Features

- **document-classification:** enhance loading state handling and update disabled logic

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.630 (2025-07-02)

This was a version bump only, there were no code changes.

## 0.0.629 (2025-07-02)

This was a version bump only, there were no code changes.

## 0.0.628 (2025-07-02)

This was a version bump only, there were no code changes.

## 0.0.627 (2025-07-01)


### 🚀 Features

- **document-classification:** add aiPrediction and rootFolderId inputs; update loading animation logic
- **document-classification:** add rootFolderIdChanged state and update loading logic
- **document-classification:** enhance loading state management with valueChanged handling

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.626 (2025-07-01)


### 🩹 Fixes

- **document-classification:** update button appearance and loading animation logic

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.625 (2025-07-01)

This was a version bump only, there were no code changes.

## 0.0.624 (2025-06-30)


### 🚀 Features

- **document-classification:** FINUI-111 implement loading state management with timeout and animations
- **document-classification:** FINUI-111 consolidate component state management and enhance loading behavior
- **document-classification:** FINUI-111 refactor state management and introduce DocumentClassificationState interface
- **document-classification:** FINUI-111 enhance loading behavior with configurable timeout and cancel option
- **document-classification:** FINUI-610 enhance state management and loading behavior
- **document-classification:** FINUI-610 implement document classification component with loading state and configurable timeout
- **document-classification:** FINUI-610 update loading state handling and add classification received logic
- **document-classification:** FINUI-610 update loading state handling and add classification received logic
- **document-classification:** FINUI-610 add delay to hide loading animation and update state management

### 🩹 Fixes

- **document-classification:** FINUI-610 correct loading state animation handling

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.623 (2025-06-30)

This was a version bump only, there were no code changes.

## 0.0.622 (2025-06-27)

This was a version bump only, there were no code changes.

## 0.0.621 (2025-06-24)

This was a version bump only, there were no code changes.

## 0.0.620 (2025-06-24)

This was a version bump only, there were no code changes.

## 0.0.619 (2025-06-24)


### 🩹 Fixes

- **.gitlab-ci.yml:** update git remote url to use PROJECT_AUTOMATION_PUSH_TOKEN instead of PUSH_TOKEN to align with variable naming convention

### ❤️  Thank You

- Jakob Boghdady

## 0.0.611 (2025-06-20)

This was a version bump only, there were no code changes.

## 0.0.610 (2025-06-20)

This was a version bump only, there were no code changes.

## 0.0.609 (2025-06-20)


### 🚀 Features

- **project:** add font assets to project configuration

### 🩹 Fixes

- **truncate-text:** correct disableTooltip type in story definition

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.608 (2025-06-19)

This was a version bump only, there were no code changes.

## 0.0.607 (2025-06-19)


### 🩹 Fixes

- **truncate-text:** ensure tooltip is enabled when text is truncated

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.606 (2025-06-18)

This was a version bump only, there were no code changes.

## 0.0.605 (2025-06-18)

This was a version bump only, there were no code changes.

## 0.0.604 (2025-06-18)

This was a version bump only, there were no code changes.

## 0.0.603 (2025-06-17)

This was a version bump only, there were no code changes.

## 0.0.602 (2025-06-17)

This was a version bump only, there were no code changes.

## 0.0.601 (2025-06-17)

This was a version bump only, there were no code changes.

## 0.0.600 (2025-06-16)

This was a version bump only, there were no code changes.

## 0.0.599 (2025-06-16)

This was a version bump only, there were no code changes.

## 0.0.598 (2025-06-13)


### 🩹 Fixes

- **breadcrumb:** refactor width handling using pxToRem utility and Renderer2

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.597 (2025-06-13)

This was a version bump only, there were no code changes.

## 0.0.596 (2025-06-12)


### 🩹 Fixes

- **text-area:** add maxlength attribute to textarea for better input control

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.595 (2025-06-12)


### 🚀 Features

- **scrollbar:** add FinPreventScrollDirective to prevent default scroll behavior

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.594 (2025-06-11)

This was a version bump only, there were no code changes.

## 0.0.593 (2025-06-11)

This was a version bump only, there were no code changes.

## 0.0.592 (2025-06-11)

This was a version bump only, there were no code changes.

## 0.0.591 (2025-06-11)

This was a version bump only, there were no code changes.

## 0.0.590 (2025-06-10)


### 🩹 Fixes

- update mat-option styles to use theme variables for font properties

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.589 (2025-06-10)


### 🩹 Fixes

- update @angular/animations version to use tilde for compatibility

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.588 (2025-06-10)

This was a version bump only, there were no code changes.

## 0.0.587 (2025-06-10)

This was a version bump only, there were no code changes.

## 0.0.586 (2025-06-10)

This was a version bump only, there were no code changes.

## 0.0.585 (2025-06-09)

This was a version bump only, there were no code changes.

## 0.0.584 (2025-06-07)

This was a version bump only, there were no code changes.

## 0.0.583 (2025-06-07)

This was a version bump only, there were no code changes.

## 0.0.582 (2025-06-06)

This was a version bump only, there were no code changes.

## 0.0.581 (2025-06-06)

This was a version bump only, there were no code changes.

## 0.0.580 (2025-06-06)

This was a version bump only, there were no code changes.

## 0.0.579 (2025-06-06)

This was a version bump only, there were no code changes.

## 0.0.578 (2025-06-06)

This was a version bump only, there were no code changes.

## 0.0.577 (2025-06-06)

This was a version bump only, there were no code changes.

## 0.0.576 (2025-06-06)

This was a version bump only, there were no code changes.

## 0.0.575 (2025-06-05)

This was a version bump only, there were no code changes.

## 0.0.574 (2025-06-05)

This was a version bump only, there were no code changes.

## 0.0.573 (2025-06-05)

This was a version bump only, there were no code changes.

## 0.0.572 (2025-06-04)

This was a version bump only, there were no code changes.

## 0.0.571 (2025-06-04)


### 🚀 Features

- **scrollbar:** FINUI-574 add smooth resize feature with duration control

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.570 (2025-06-04)


### 🚀 Features

- **dropdown:** update toggle behavior for options panel
- **field-messages:** and add new story examples for radio and checkbox components

### 🩹 Fixes

- update form control validation and improve message selection logging
- update readonly behavior for file input and adjust cursor style
- update side panel styles for sticky behavior and transition effects
- **dropdown:** add optional chaining for toggle options panel button reference

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.569 (2025-06-04)

This was a version bump only, there were no code changes.

## 0.0.568 (2025-06-03)

This was a version bump only, there were no code changes.

## 0.0.567 (2025-06-03)

This was a version bump only, there were no code changes.

## 0.0.566 (2025-06-03)

This was a version bump only, there were no code changes.

## 0.0.565 (2025-06-03)

This was a version bump only, there were no code changes.

## 0.0.564 (2025-06-02)


### 🩹 Fixes

- **theme:** refactor theme handling in Storybook preview configuration

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.563 (2025-06-02)


### 🩹 Fixes

- **table:** update expanded-detail-row to use grid layout for smoother transitions

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.562 (2025-06-02)


### 🩹 Fixes

- **table:** add transition and interpolate-size to expanded-detail-row

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.561 (2025-06-02)

This was a version bump only, there were no code changes.

## 0.0.560 (2025-05-30)

This was a version bump only, there were no code changes.

## 0.0.559 (2025-05-30)

This was a version bump only, there were no code changes.

## 0.0.558 (2025-05-29)

This was a version bump only, there were no code changes.

## 0.0.557 (2025-05-29)

This was a version bump only, there were no code changes.

## 0.0.556 (2025-05-29)

This was a version bump only, there were no code changes.

## 0.0.555 (2025-05-29)

This was a version bump only, there were no code changes.

## 0.0.554 (2025-05-29)

This was a version bump only, there were no code changes.

## 0.0.553 (2025-05-28)

This was a version bump only, there were no code changes.

## 0.0.552 (2025-05-28)


### 🩹 Fixes

- correct form control reference in date picker component

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.551 (2025-05-28)

This was a version bump only, there were no code changes.

## 0.0.550 (2025-05-28)

This was a version bump only, there were no code changes.

## 0.0.549 (2025-05-28)

This was a version bump only, there were no code changes.

## 0.0.548 (2025-05-28)


### 🩹 Fixes

- update side panel styles for sticky behavior and transition effects

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.547 (2025-05-27)


### 🩹 Fixes

- update readonly behavior for file input and adjust cursor style

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.546 (2025-05-27)


### 🩹 Fixes

- update form control validation and improve message selection logging

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.545 (2025-05-27)

This was a version bump only, there were no code changes.

## 0.0.544 (2025-05-27)

This was a version bump only, there were no code changes.

## 0.0.543 (2025-05-27)


### 🩹 Fixes

- **dropdown:** add optional chaining for toggle options panel button reference

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.542 (2025-05-23)


### 🚀 Features

- **dropdown:** update toggle behavior for options panel
- **field-messages:** and add new story examples for radio and checkbox components

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.541 (2025-05-23)

This was a version bump only, there were no code changes.

## 0.0.540 (2025-05-23)

This was a version bump only, there were no code changes.

## 0.0.539 (2025-05-22)


### 🩹 Fixes

- **dropdown:** add MatDialogRef to check if the component is in modal window and there is validation error to update the position of the autocomplete panel

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.538 (2025-05-22)


### 🩹 Fixes

- **dropdown:** improve control value handling and rename toggle function

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.537 (2025-05-22)

This was a version bump only, there were no code changes.

## 0.0.536 (2025-05-22)


### 🩹 Fixes

- **dropdown:** improve suffix handling and clear button interaction

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.535 (2025-05-22)

This was a version bump only, there were no code changes.

## 0.0.534 (2025-05-22)

This was a version bump only, there were no code changes.

## 0.0.533 (2025-05-21)


### 🩹 Fixes

- **dropdown:** add suffix wrapper and improve control value handling
- **gitignore:** add rules for cursor and GitHub instructions files

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.532 (2025-05-21)

This was a version bump only, there were no code changes.

## 0.0.531 (2025-05-21)

This was a version bump only, there were no code changes.

## 0.0.530 (2025-05-21)

This was a version bump only, there were no code changes.

## 0.0.529 (2025-05-21)

This was a version bump only, there were no code changes.

## 0.0.528 (2025-05-21)

This was a version bump only, there were no code changes.

## 0.0.527 (2025-05-20)

This was a version bump only, there were no code changes.

## 0.0.526 (2025-05-20)

This was a version bump only, there were no code changes.

## 0.0.525 (2025-05-20)

This was a version bump only, there were no code changes.

## 0.0.524 (2025-05-20)

This was a version bump only, there were no code changes.

## 0.0.523 (2025-05-20)

This was a version bump only, there were no code changes.

## 0.0.522 (2025-05-19)

This was a version bump only, there were no code changes.

## 0.0.521 (2025-05-19)

This was a version bump only, there were no code changes.

## 0.0.520 (2025-05-19)

This was a version bump only, there were no code changes.

## 0.0.519 (2025-05-16)

This was a version bump only, there were no code changes.

## 0.0.518 (2025-05-16)

This was a version bump only, there were no code changes.

## 0.0.517 (2025-05-15)

This was a version bump only, there were no code changes.

## 0.0.516 (2025-05-15)

This was a version bump only, there were no code changes.

## 0.0.515 (2025-05-15)

This was a version bump only, there were no code changes.

## 0.0.514 (2025-05-15)

This was a version bump only, there were no code changes.

## 0.0.513 (2025-05-13)


### 🚀 Features

- **date-picker:** enable showIcon and update placeholder format

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.512 (2025-05-13)

This was a version bump only, there were no code changes.

## 0.0.511 (2025-05-13)

This was a version bump only, there were no code changes.

## 0.0.510 (2025-05-13)


### 🚀 Features

- add FIN_LOCALE_ID injection token and update DateRangePipe to use localeId

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.509 (2025-05-13)

This was a version bump only, there were no code changes.

## 0.0.508 (2025-05-13)

This was a version bump only, there were no code changes.

## 0.0.507 (2025-05-13)

This was a version bump only, there were no code changes.

## 0.0.506 (2025-05-13)

This was a version bump only, there were no code changes.

## 0.0.505 (2025-05-12)

This was a version bump only, there were no code changes.

## 0.0.504 (2025-05-12)

This was a version bump only, there were no code changes.

## 0.0.503 (2025-05-12)

This was a version bump only, there were no code changes.

## 0.0.502 (2025-05-12)


### 🚀 Features

- **search:** add highlight pipe and integrate with autocomplete options

### ❤️  Thank You

- ivaylo.cherkezov

## 0.0.501 (2025-05-12)

This was a version bump only, there were no code changes.

## 0.0.500 (2025-05-12)

This was a version bump only, there were no code changes.

## 0.0.499 (2025-05-12)

This was a version bump only, there were no code changes.

## 0.0.498 (2025-05-12)

This was a version bump only, there were no code changes.

## 0.0.497 (2025-05-12)

This was a version bump only, there were no code changes.

## 0.0.496 (2025-05-12)

This was a version bump only, there were no code changes.

## 0.0.495 (2025-05-11)

This was a version bump only, there were no code changes.

## 0.0.494 (2025-05-09)

This was a version bump only, there were no code changes.

## 0.0.493 (2025-05-09)

This was a version bump only, there were no code changes.

## 0.0.492 (2025-05-09)

This was a version bump only, there were no code changes.

## 0.0.491 (2025-05-09)

This was a version bump only, there were no code changes.

## 0.0.490 (2025-05-09)

This was a version bump only, there were no code changes.

## 0.0.489 (2025-05-08)

This was a version bump only, there were no code changes.

## 0.0.488 (2025-05-08)

This was a version bump only, there were no code changes.

## 0.0.487 (2025-05-08)

This was a version bump only, there were no code changes.

## 0.0.486 (2025-05-08)

This was a version bump only, there were no code changes.

## 0.0.485 (2025-05-08)

This was a version bump only, there were no code changes.

## 0.0.484 (2025-05-08)

This was a version bump only, there were no code changes.

## 0.0.483 (2025-05-08)

This was a version bump only, there were no code changes.

## 0.0.482 (2025-05-08)

This was a version bump only, there were no code changes.

## 0.0.481 (2025-05-07)

This was a version bump only, there were no code changes.

## 0.0.480 (2025-05-07)

This was a version bump only, there were no code changes.

## 0.0.479 (2025-05-07)

This was a version bump only, there were no code changes.

## 0.0.478 (2025-05-07)

This was a version bump only, there were no code changes.

## 0.0.477 (2025-05-05)

This was a version bump only, there were no code changes.

## 0.0.476 (2025-05-05)

This was a version bump only, there were no code changes.

## 0.0.475 (2025-05-05)

This was a version bump only, there were no code changes.

## 0.0.474 (2025-05-05)

This was a version bump only, there were no code changes.

## 0.0.473 (2025-05-05)

This was a version bump only, there were no code changes.

## 0.0.472 (2025-05-04)

This was a version bump only, there were no code changes.

## 0.0.471 (2025-05-02)

This was a version bump only, there were no code changes.

## 0.0.470 (2025-04-30)

This was a version bump only, there were no code changes.

## 0.0.469 (2025-04-30)

This was a version bump only, there were no code changes.

## 0.0.468 (2025-04-30)

This was a version bump only, there were no code changes.

## 0.0.467 (2025-04-30)

This was a version bump only, there were no code changes.

## 0.0.466 (2025-04-29)

This was a version bump only, there were no code changes.

## 0.0.465 (2025-04-29)

This was a version bump only, there were no code changes.

## 0.0.464 (2025-04-29)

This was a version bump only, there were no code changes.

## 0.0.463 (2025-04-29)

This was a version bump only, there were no code changes.

## 0.0.462 (2025-04-29)

This was a version bump only, there were no code changes.

## 0.0.461 (2025-04-29)

This was a version bump only, there were no code changes.

## 0.0.460 (2025-04-28)

This was a version bump only, there were no code changes.

## 0.0.459 (2025-04-28)

This was a version bump only, there were no code changes.

## 0.0.458 (2025-04-28)

This was a version bump only, there were no code changes.

## 0.0.457 (2025-04-28)

This was a version bump only, there were no code changes.

## 0.0.456 (2025-04-28)

This was a version bump only, there were no code changes.

## 0.0.455 (2025-04-28)

This was a version bump only, there were no code changes.

## 0.0.454 (2025-04-28)

This was a version bump only, there were no code changes.

## 0.0.453 (2025-04-25)

This was a version bump only, there were no code changes.

## 0.0.452 (2025-04-24)

This was a version bump only, there were no code changes.

## 0.0.451 (2025-04-24)

This was a version bump only, there were no code changes.

## 0.0.450 (2025-04-24)

This was a version bump only, there were no code changes.

## 0.0.449 (2025-04-24)

This was a version bump only, there were no code changes.

## 0.0.448 (2025-04-22)

This was a version bump only, there were no code changes.

## 0.0.447 (2025-04-17)

This was a version bump only, there were no code changes.

## 0.0.446 (2025-04-17)

This was a version bump only, there were no code changes.

## 0.0.445 (2025-04-17)

This was a version bump only, there were no code changes.

## 0.0.444 (2025-04-17)

This was a version bump only, there were no code changes.

## 0.0.443 (2025-04-16)

This was a version bump only, there were no code changes.

## 0.0.442 (2025-04-16)

This was a version bump only, there were no code changes.

## 0.0.441 (2025-04-16)

This was a version bump only, there were no code changes.

## 0.0.440 (2025-04-15)

This was a version bump only, there were no code changes.

## 0.0.439 (2025-04-15)

This was a version bump only, there were no code changes.

## 0.0.438 (2025-04-15)

This was a version bump only, there were no code changes.

## 0.0.437 (2025-04-15)

This was a version bump only, there were no code changes.

## 0.0.436 (2025-04-14)

This was a version bump only, there were no code changes.

## 0.0.435 (2025-04-12)

This was a version bump only, there were no code changes.

## 0.0.434 (2025-04-11)

This was a version bump only, there were no code changes.

## 0.0.433 (2025-04-11)

This was a version bump only, there were no code changes.

## 0.0.432 (2025-04-11)

This was a version bump only, there were no code changes.

## 0.0.431 (2025-04-11)

This was a version bump only, there were no code changes.

## 0.0.430 (2025-04-11)

This was a version bump only, there were no code changes.

## 0.0.429 (2025-04-10)

This was a version bump only, there were no code changes.

## 0.0.428 (2025-04-10)

This was a version bump only, there were no code changes.

## 0.0.427 (2025-04-10)

This was a version bump only, there were no code changes.

## 0.0.426 (2025-04-10)

This was a version bump only, there were no code changes.

## 0.0.425 (2025-04-10)

This was a version bump only, there were no code changes.

## 0.0.424 (2025-04-10)

This was a version bump only, there were no code changes.

## 0.0.423 (2025-04-10)

This was a version bump only, there were no code changes.

## 0.0.422 (2025-04-10)

This was a version bump only, there were no code changes.

## 0.0.421 (2025-04-10)

This was a version bump only, there were no code changes.

## 0.0.420 (2025-04-09)

This was a version bump only, there were no code changes.

## 0.0.419 (2025-04-09)

This was a version bump only, there were no code changes.

## 0.0.418 (2025-04-08)

This was a version bump only, there were no code changes.

## 0.0.417 (2025-04-08)

This was a version bump only, there were no code changes.

## 0.0.416 (2025-04-08)

This was a version bump only, there were no code changes.

## 0.0.415 (2025-04-07)

This was a version bump only, there were no code changes.

## 0.0.414 (2025-04-07)

This was a version bump only, there were no code changes.

## 0.0.413 (2025-04-07)

This was a version bump only, there were no code changes.

## 0.0.412 (2025-04-05)

This was a version bump only, there were no code changes.

## 0.0.411 (2025-04-04)

This was a version bump only, there were no code changes.

## 0.0.410 (2025-04-04)

This was a version bump only, there were no code changes.

## 0.0.409 (2025-04-04)

This was a version bump only, there were no code changes.

## 0.0.408 (2025-04-04)

This was a version bump only, there were no code changes.

## 0.0.407 (2025-04-04)

This was a version bump only, there were no code changes.

## 0.0.406 (2025-04-04)

This was a version bump only, there were no code changes.

## 0.0.405 (2025-04-04)

This was a version bump only, there were no code changes.

## 0.0.404 (2025-04-04)

This was a version bump only, there were no code changes.

## 0.0.403 (2025-04-04)

This was a version bump only, there were no code changes.

## 0.0.402 (2025-04-03)

This was a version bump only, there were no code changes.

## 0.0.401 (2025-04-03)

This was a version bump only, there were no code changes.

## 0.0.400 (2025-04-02)

This was a version bump only, there were no code changes.

## 0.0.399 (2025-04-02)

This was a version bump only, there were no code changes.

## 0.0.398 (2025-04-02)

This was a version bump only, there were no code changes.

## 0.0.397 (2025-04-02)

This was a version bump only, there were no code changes.

## 0.0.396 (2025-04-02)

This was a version bump only, there were no code changes.

## 0.0.395 (2025-04-01)

This was a version bump only, there were no code changes.

## 0.0.394 (2025-04-01)

This was a version bump only, there were no code changes.

## 0.0.393 (2025-04-01)

This was a version bump only, there were no code changes.

## 0.0.392 (2025-03-31)

This was a version bump only, there were no code changes.

## 0.0.391 (2025-03-31)

This was a version bump only, there were no code changes.

## 0.0.390 (2025-03-31)

This was a version bump only, there were no code changes.

## 0.0.389 (2025-03-31)

This was a version bump only, there were no code changes.

## 0.0.388 (2025-03-31)

This was a version bump only, there were no code changes.

## 0.0.387 (2025-03-31)

This was a version bump only, there were no code changes.

## 0.0.386 (2025-03-28)

This was a version bump only, there were no code changes.

## 0.0.385 (2025-03-28)

This was a version bump only, there were no code changes.

## 0.0.384 (2025-03-28)

This was a version bump only, there were no code changes.

## 0.0.383 (2025-03-28)

This was a version bump only, there were no code changes.

## 0.0.382 (2025-03-28)

This was a version bump only, there were no code changes.

## 0.0.381 (2025-03-28)

This was a version bump only, there were no code changes.

## 0.0.380 (2025-03-27)

This was a version bump only, there were no code changes.

## 0.0.379 (2025-03-27)

This was a version bump only, there were no code changes.

## 0.0.378 (2025-03-27)

This was a version bump only, there were no code changes.

## 0.0.377 (2025-03-27)

This was a version bump only, there were no code changes.

## 0.0.376 (2025-03-26)

This was a version bump only, there were no code changes.

## 0.0.375 (2025-03-26)

This was a version bump only, there were no code changes.

## 0.0.374 (2025-03-26)

This was a version bump only, there were no code changes.

## 0.0.373 (2025-03-26)

This was a version bump only, there were no code changes.

## 0.0.372 (2025-03-24)

This was a version bump only, there were no code changes.

## 0.0.371 (2025-03-24)

This was a version bump only, there were no code changes.

## 0.0.370 (2025-03-21)

This was a version bump only, there were no code changes.

## 0.0.369 (2025-03-21)

This was a version bump only, there were no code changes.

## 0.0.368 (2025-03-21)

This was a version bump only, there were no code changes.

## 0.0.367 (2025-03-20)

This was a version bump only, there were no code changes.

## 0.0.366 (2025-03-20)

This was a version bump only, there were no code changes.

## 0.0.365 (2025-03-20)

This was a version bump only, there were no code changes.

## 0.0.364 (2025-03-20)

This was a version bump only, there were no code changes.

## 0.0.363 (2025-03-20)

This was a version bump only, there were no code changes.

## 0.0.362 (2025-03-20)

This was a version bump only, there were no code changes.

## 0.0.361 (2025-03-20)

This was a version bump only, there were no code changes.

## 0.0.360 (2025-03-20)

This was a version bump only, there were no code changes.

## 0.0.359 (2025-03-20)

This was a version bump only, there were no code changes.

## 0.0.358 (2025-03-19)

This was a version bump only, there were no code changes.

## 0.0.357 (2025-03-18)

This was a version bump only, there were no code changes.

## 0.0.356 (2025-03-18)

This was a version bump only, there were no code changes.

## 0.0.355 (2025-03-17)

This was a version bump only, there were no code changes.

## 0.0.354 (2025-03-17)

This was a version bump only, there were no code changes.

## 0.0.353 (2025-03-17)

This was a version bump only, there were no code changes.

## 0.0.352 (2025-03-17)

This was a version bump only, there were no code changes.

## 0.0.351 (2025-03-17)

This was a version bump only, there were no code changes.

## 0.0.350 (2025-03-17)

This was a version bump only, there were no code changes.

## 0.0.349 (2025-03-17)

This was a version bump only, there were no code changes.

## 0.0.348 (2025-03-17)

This was a version bump only, there were no code changes.

## 0.0.347 (2025-03-14)

This was a version bump only, there were no code changes.

## 0.0.346 (2025-03-14)

This was a version bump only, there were no code changes.

## 0.0.345 (2025-03-14)

This was a version bump only, there were no code changes.

## 0.0.344 (2025-03-14)

This was a version bump only, there were no code changes.

## 0.0.343 (2025-03-14)

This was a version bump only, there were no code changes.

## 0.0.342 (2025-03-14)

This was a version bump only, there were no code changes.

## 0.0.341 (2025-03-13)

This was a version bump only, there were no code changes.

## 0.0.340 (2025-03-13)

This was a version bump only, there were no code changes.

## 0.0.339 (2025-03-13)

This was a version bump only, there were no code changes.

## 0.0.338 (2025-03-13)

This was a version bump only, there were no code changes.

## 0.0.337 (2025-03-12)

This was a version bump only, there were no code changes.

## 0.0.336 (2025-03-12)

This was a version bump only, there were no code changes.

## 0.0.335 (2025-03-12)

This was a version bump only, there were no code changes.

## 0.0.334 (2025-03-12)

This was a version bump only, there were no code changes.

## 0.0.333 (2025-03-12)

This was a version bump only, there were no code changes.

## 0.0.332 (2025-03-12)

This was a version bump only, there were no code changes.

## 0.0.331 (2025-03-12)

This was a version bump only, there were no code changes.

## 0.0.330 (2025-03-11)

This was a version bump only, there were no code changes.

## 0.0.329 (2025-03-11)

This was a version bump only, there were no code changes.

## 0.0.328 (2025-03-11)

This was a version bump only, there were no code changes.

## 0.0.327 (2025-03-11)

This was a version bump only, there were no code changes.

## 0.0.326 (2025-03-11)

This was a version bump only, there were no code changes.

## 0.0.325 (2025-03-10)

This was a version bump only, there were no code changes.

## 0.0.324 (2025-03-10)

This was a version bump only, there were no code changes.

## 0.0.323 (2025-03-10)

This was a version bump only, there were no code changes.

## 0.0.322 (2025-03-07)

This was a version bump only, there were no code changes.

## 0.0.321 (2025-03-07)

This was a version bump only, there were no code changes.

## 0.0.320 (2025-03-07)

This was a version bump only, there were no code changes.

## 0.0.319 (2025-03-06)

This was a version bump only, there were no code changes.

## 0.0.318 (2025-03-06)

This was a version bump only, there were no code changes.

## 0.0.317 (2025-03-06)

This was a version bump only, there were no code changes.

## 0.0.316 (2025-03-06)

This was a version bump only, there were no code changes.

## 0.0.315 (2025-03-05)

This was a version bump only, there were no code changes.

## 0.0.314 (2025-03-04)

This was a version bump only, there were no code changes.

## 0.0.313 (2025-03-04)

This was a version bump only, there were no code changes.

## 0.0.312 (2025-03-04)

This was a version bump only, there were no code changes.

## 0.0.311 (2025-03-03)

This was a version bump only, there were no code changes.

## 0.0.310 (2025-03-02)

This was a version bump only, there were no code changes.

## 0.0.309 (2025-03-01)

This was a version bump only, there were no code changes.

## 0.0.308 (2025-02-28)

This was a version bump only, there were no code changes.

## 0.0.307 (2025-02-27)

This was a version bump only, there were no code changes.

## 0.0.306 (2025-02-27)

This was a version bump only, there were no code changes.

## 0.0.305 (2025-02-27)

This was a version bump only, there were no code changes.

## 0.0.304 (2025-02-27)

This was a version bump only, there were no code changes.

## 0.0.303 (2025-02-27)

This was a version bump only, there were no code changes.

## 0.0.302 (2025-02-27)

This was a version bump only, there were no code changes.

## 0.0.301 (2025-02-27)

This was a version bump only, there were no code changes.

## 0.0.300 (2025-02-27)

This was a version bump only, there were no code changes.

## 0.0.299 (2025-02-27)

This was a version bump only, there were no code changes.

## 0.0.298 (2025-02-25)

This was a version bump only, there were no code changes.

## 0.0.297 (2025-02-25)

This was a version bump only, there were no code changes.

## 0.0.296 (2025-02-25)

This was a version bump only, there were no code changes.

## 0.0.295 (2025-02-25)

This was a version bump only, there were no code changes.

## 0.0.294 (2025-02-25)

This was a version bump only, there were no code changes.

## 0.0.293 (2025-02-25)

This was a version bump only, there were no code changes.

## 0.0.292 (2025-02-24)

This was a version bump only, there were no code changes.

## 0.0.291 (2025-02-24)

This was a version bump only, there were no code changes.

## 0.0.290 (2025-02-20)

This was a version bump only, there were no code changes.

## 0.0.289 (2025-02-20)

This was a version bump only, there were no code changes.

## 0.0.288 (2025-02-20)

This was a version bump only, there were no code changes.

## 0.0.287 (2025-02-20)

This was a version bump only, there were no code changes.

## 0.0.286 (2025-02-20)

This was a version bump only, there were no code changes.

## 0.0.285 (2025-02-20)

This was a version bump only, there were no code changes.

## 0.0.284 (2025-02-19)

This was a version bump only, there were no code changes.

## 0.0.283 (2025-02-19)

This was a version bump only, there were no code changes.

## 0.0.282 (2025-02-19)

This was a version bump only, there were no code changes.

## 0.0.281 (2025-02-18)

This was a version bump only, there were no code changes.

## 0.0.280 (2025-02-18)

This was a version bump only, there were no code changes.

## 0.0.279 (2025-02-18)

This was a version bump only, there were no code changes.

## 0.0.278 (2025-02-18)

This was a version bump only, there were no code changes.

## 0.0.277 (2025-02-17)

This was a version bump only, there were no code changes.

## 0.0.276 (2025-02-17)

This was a version bump only, there were no code changes.

## 0.0.275 (2025-02-16)

This was a version bump only, there were no code changes.

## 0.0.274 (2025-02-15)

This was a version bump only, there were no code changes.

## 0.0.273 (2025-02-15)

This was a version bump only, there were no code changes.

## 0.0.272 (2025-02-15)

This was a version bump only, there were no code changes.

## 0.0.271 (2025-02-14)

This was a version bump only, there were no code changes.

## 0.0.270 (2025-02-14)

This was a version bump only, there were no code changes.

## 0.0.269 (2025-02-14)

This was a version bump only, there were no code changes.

## 0.0.268 (2025-02-13)

This was a version bump only, there were no code changes.

## 0.0.267 (2025-02-13)

This was a version bump only, there were no code changes.

## 0.0.266 (2025-02-13)

This was a version bump only, there were no code changes.

## 0.0.265 (2025-02-13)

This was a version bump only, there were no code changes.

## 0.0.264 (2025-02-13)

This was a version bump only, there were no code changes.

## 0.0.263 (2025-02-13)

This was a version bump only, there were no code changes.

## 0.0.262 (2025-02-12)

This was a version bump only, there were no code changes.

## 0.0.261 (2025-02-12)

This was a version bump only, there were no code changes.

## 0.0.260 (2025-02-12)

This was a version bump only, there were no code changes.

## 0.0.259 (2025-02-11)

This was a version bump only, there were no code changes.

## 0.0.258 (2025-02-11)

This was a version bump only, there were no code changes.

## 0.0.257 (2025-02-10)

This was a version bump only, there were no code changes.

## 0.0.256 (2025-02-10)

This was a version bump only, there were no code changes.

## 0.0.255 (2025-02-10)

This was a version bump only, there were no code changes.

## 0.0.254 (2025-02-10)

This was a version bump only, there were no code changes.

## 0.0.253 (2025-02-10)

This was a version bump only, there were no code changes.

## 0.0.252 (2025-02-10)

This was a version bump only, there were no code changes.

## 0.0.251 (2025-02-07)

This was a version bump only, there were no code changes.

## 0.0.250 (2025-02-07)

This was a version bump only, there were no code changes.

## 0.0.249 (2025-02-06)

This was a version bump only, there were no code changes.

## 0.0.248 (2025-02-06)

This was a version bump only, there were no code changes.

## 0.0.247 (2025-02-06)

This was a version bump only, there were no code changes.

## 0.0.246 (2025-02-05)

This was a version bump only, there were no code changes.

## 0.0.245 (2025-02-04)

This was a version bump only, there were no code changes.

## 0.0.244 (2025-02-04)

This was a version bump only, there were no code changes.

## 0.0.243 (2025-02-04)

This was a version bump only, there were no code changes.

## 0.0.242 (2025-02-04)

This was a version bump only, there were no code changes.

## 0.0.241 (2025-02-03)

This was a version bump only, there were no code changes.

## 0.0.240 (2025-02-03)

This was a version bump only, there were no code changes.

## 0.0.239 (2025-02-03)

This was a version bump only, there were no code changes.

## 0.0.238 (2025-02-02)

This was a version bump only, there were no code changes.

## 0.0.237 (2025-01-31)

This was a version bump only, there were no code changes.

## 0.0.236 (2025-01-30)

This was a version bump only, there were no code changes.

## 0.0.235 (2025-01-30)

This was a version bump only, there were no code changes.

## 0.0.234 (2025-01-30)

This was a version bump only, there were no code changes.

## 0.0.233 (2025-01-30)

This was a version bump only, there were no code changes.

## 0.0.232 (2025-01-30)

This was a version bump only, there were no code changes.

## 0.0.231 (2025-01-30)

This was a version bump only, there were no code changes.

## 0.0.230 (2025-01-30)

This was a version bump only, there were no code changes.

## 0.0.229 (2025-01-29)

This was a version bump only, there were no code changes.

## 0.0.228 (2025-01-29)

This was a version bump only, there were no code changes.

## 0.0.226 (2025-01-29)

This was a version bump only, there were no code changes.

## 0.0.225 (2025-01-28)

This was a version bump only, there were no code changes.

## 0.0.224 (2025-01-28)

This was a version bump only, there were no code changes.

## 0.0.223 (2025-01-27)

This was a version bump only, there were no code changes.

## 0.0.222 (2025-01-24)

This was a version bump only, there were no code changes.

## 0.0.221 (2025-01-24)

This was a version bump only, there were no code changes.

## 0.0.220 (2025-01-23)

This was a version bump only, there were no code changes.

## 0.0.219 (2025-01-23)

This was a version bump only, there were no code changes.

## 0.0.218 (2025-01-23)

This was a version bump only, there were no code changes.

## 0.0.217 (2025-01-23)

This was a version bump only, there were no code changes.

## 0.0.216 (2025-01-21)

This was a version bump only, there were no code changes.

## 0.0.215 (2025-01-20)

This was a version bump only, there were no code changes.

## 0.0.214 (2025-01-20)

This was a version bump only, there were no code changes.

## 0.0.213 (2025-01-18)

This was a version bump only, there were no code changes.

## 0.0.212 (2025-01-18)

This was a version bump only, there were no code changes.

## 0.0.211 (2025-01-18)

This was a version bump only, there were no code changes.

## 0.0.210 (2025-01-17)

This was a version bump only, there were no code changes.

## 0.0.209 (2025-01-17)

This was a version bump only, there were no code changes.

## 0.0.208 (2025-01-17)

This was a version bump only, there were no code changes.

## 0.0.207 (2025-01-17)

This was a version bump only, there were no code changes.

## 0.0.206 (2025-01-16)

This was a version bump only, there were no code changes.

## 0.0.205 (2025-01-16)

This was a version bump only, there were no code changes.

## 0.0.204 (2025-01-16)

This was a version bump only, there were no code changes.

## 0.0.203 (2025-01-16)

This was a version bump only, there were no code changes.

## 0.0.202 (2025-01-16)

This was a version bump only, there were no code changes.

## 0.0.201 (2025-01-16)

This was a version bump only, there were no code changes.

## 0.0.200 (2025-01-15)

This was a version bump only, there were no code changes.

## 0.0.199 (2025-01-15)

This was a version bump only, there were no code changes.

## 0.0.198 (2025-01-14)

This was a version bump only, there were no code changes.

## 0.0.197 (2025-01-14)

This was a version bump only, there were no code changes.

## 0.0.196 (2025-01-14)

This was a version bump only, there were no code changes.

## 0.0.195 (2025-01-09)

This was a version bump only, there were no code changes.

## 0.0.194 (2025-01-08)

This was a version bump only, there were no code changes.

## 0.0.193 (2025-01-06)

This was a version bump only, there were no code changes.

## 0.0.192 (2025-01-06)

This was a version bump only, there were no code changes.

## 0.0.191 (2025-01-03)

This was a version bump only, there were no code changes.

## 0.0.190 (2025-01-02)

This was a version bump only, there were no code changes.

## 0.0.189 (2025-01-02)

This was a version bump only, there were no code changes.

## 0.0.188 (2024-12-18)

This was a version bump only, there were no code changes.

## 0.0.187 (2024-12-18)

This was a version bump only, there were no code changes.

## 0.0.186 (2024-12-18)

This was a version bump only, there were no code changes.

## 0.0.185 (2024-12-17)

This was a version bump only, there were no code changes.

## 0.0.184 (2024-12-17)

This was a version bump only, there were no code changes.

## 0.0.183 (2024-12-17)

This was a version bump only, there were no code changes.

## 0.0.182 (2024-12-17)

This was a version bump only, there were no code changes.

## 0.0.181 (2024-12-17)

This was a version bump only, there were no code changes.

## 0.0.180 (2024-12-17)

This was a version bump only, there were no code changes.

## 0.0.179 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.178 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.177 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.176 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.175 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.174 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.173 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.172 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.171 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.170 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.169 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.168 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.167 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.166 (2024-12-16)

This was a version bump only, there were no code changes.

## 0.0.165 (2024-12-14)

This was a version bump only, there were no code changes.

## 0.0.164 (2024-12-13)

This was a version bump only, there were no code changes.

## 0.0.163 (2024-12-13)

This was a version bump only, there were no code changes.

## 0.0.162 (2024-12-13)

This was a version bump only, there were no code changes.

## 0.0.161 (2024-12-13)

This was a version bump only, there were no code changes.

## 0.0.160 (2024-12-12)

This was a version bump only, there were no code changes.

## 0.0.159 (2024-12-12)

This was a version bump only, there were no code changes.

## 0.0.158 (2024-12-12)

This was a version bump only, there were no code changes.

## 0.0.157 (2024-12-12)

This was a version bump only, there were no code changes.

## 0.0.156 (2024-12-12)

This was a version bump only, there were no code changes.

## 0.0.155 (2024-12-12)

This was a version bump only, there were no code changes.

## 0.0.154 (2024-12-10)

This was a version bump only, there were no code changes.

## 0.0.153 (2024-12-10)

This was a version bump only, there were no code changes.

## 0.0.152 (2024-12-10)

This was a version bump only, there were no code changes.

## 0.0.151 (2024-12-09)

This was a version bump only, there were no code changes.

## 0.0.150 (2024-12-09)

This was a version bump only, there were no code changes.

## 0.0.149 (2024-12-09)

This was a version bump only, there were no code changes.

## 0.0.148 (2024-12-09)

This was a version bump only, there were no code changes.

## 0.0.147 (2024-12-06)

This was a version bump only, there were no code changes.

## 0.0.146 (2024-12-05)

This was a version bump only, there were no code changes.

## 0.0.145 (2024-12-05)

This was a version bump only, there were no code changes.

## 0.0.144 (2024-12-05)

This was a version bump only, there were no code changes.

## 0.0.143 (2024-12-05)

This was a version bump only, there were no code changes.

## 0.0.142 (2024-12-05)

This was a version bump only, there were no code changes.

## 0.0.141 (2024-12-05)

This was a version bump only, there were no code changes.

## 0.0.140 (2024-12-05)

This was a version bump only, there were no code changes.

## 0.0.139 (2024-12-05)

This was a version bump only, there were no code changes.

## 0.0.138 (2024-12-05)

This was a version bump only, there were no code changes.

## 0.0.136 (2024-12-04)

This was a version bump only, there were no code changes.

## 0.0.135 (2024-12-04)

This was a version bump only, there were no code changes.

## 0.0.134 (2024-12-04)

This was a version bump only, there were no code changes.

## 0.0.133 (2024-12-04)

This was a version bump only, there were no code changes.

## 0.0.132 (2024-12-02)

This was a version bump only, there were no code changes.

## 0.0.131 (2024-11-29)

This was a version bump only, there were no code changes.

## 0.0.130 (2024-11-29)

This was a version bump only, there were no code changes.

## 0.0.129 (2024-11-29)

This was a version bump only, there were no code changes.

## 0.0.128 (2024-11-29)

This was a version bump only, there were no code changes.

## 0.0.127 (2024-11-29)

This was a version bump only, there were no code changes.

## 0.0.126 (2024-11-29)

This was a version bump only, there were no code changes.

## 0.0.125 (2024-11-28)

This was a version bump only, there were no code changes.

## 0.0.124 (2024-11-28)

This was a version bump only, there were no code changes.

## 0.0.123 (2024-11-28)

This was a version bump only, there were no code changes.

## 0.0.122 (2024-11-26)

This was a version bump only, there were no code changes.

## 0.0.121 (2024-11-26)

This was a version bump only, there were no code changes.

## 0.0.120 (2024-11-26)

This was a version bump only, there were no code changes.

## 0.0.119 (2024-11-26)

This was a version bump only, there were no code changes.

## 0.0.118 (2024-11-26)

This was a version bump only, there were no code changes.

## 0.0.116 (2024-11-26)

This was a version bump only, there were no code changes.

## 0.0.115 (2024-11-26)

This was a version bump only, there were no code changes.

## 0.0.114 (2024-11-26)

This was a version bump only, there were no code changes.

## 0.0.113 (2024-11-25)

This was a version bump only, there were no code changes.

## 0.0.112 (2024-11-25)

This was a version bump only, there were no code changes.

## 0.0.110 (2024-11-25)

This was a version bump only, there were no code changes.

## 0.0.109 (2024-11-22)

This was a version bump only, there were no code changes.

## 0.0.108 (2024-11-22)

This was a version bump only, there were no code changes.

## 0.0.107 (2024-11-22)

This was a version bump only, there were no code changes.

## 0.0.106 (2024-11-20)

This was a version bump only, there were no code changes.

## 0.0.105 (2024-11-20)

This was a version bump only, there were no code changes.

## 0.0.104 (2024-11-19)

This was a version bump only, there were no code changes.

## 0.0.103 (2024-11-19)

This was a version bump only, there were no code changes.

## 0.0.102 (2024-11-19)

This was a version bump only, there were no code changes.

## 0.0.101 (2024-11-18)

This was a version bump only, there were no code changes.

## 0.0.99 (2024-11-17)

This was a version bump only, there were no code changes.

# Changelog


## 0.0.97 (2024-11-17)

### chore

* [e8fde] chore(release): publish 0.0.98 (ivaylo.cherkezov)

