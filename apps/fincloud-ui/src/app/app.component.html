<div style="width: 400px; margin: 0 auto;">
  <fin-tree-menu [data]="menuData" [alwaysExpand]="true" (finDropListDropped)="onDrop($event)" (finTreeNodeToggle)="onNodeToggle($event)">
    <ng-template finTreeNode let-node [draggable]="state$" [nodeClick]="onNodeClick" [hideExpandButton]="true">
      <fin-tree-node-counter [node]="node">
        <ng-container finTreeNodeCounterPrefix>
          #
        </ng-container>
        <ng-container finTreeNodeCounterSuffix>
          .
        </ng-container>
      </fin-tree-node-counter>
      {{ node.name }}
    </ng-template>
    <ng-template finTreeNode let-node templateName="field" [appearance]="appearance.SECONDARY" [draggable]="state$">
      {{ node.name }}
    </ng-template>
    <ng-template finTreeNode let-node templateName="folderRoot" [appearance]="appearance.SECONDARY">
      <span class="fin-font-semibold">{{ node.name }}</span>
    </ng-template>
    <ng-template finTreeNode let-node templateName="folder" [appearance]="appearance.SECONDARY">
      <fin-icon name="folder" [size]="size.S" />
      {{ node.name }}
    </ng-template>
    <ng-template finTreeNode let-node templateName="document" [appearance]="appearance.SECONDARY">
      <fin-icon name="article" [size]="size.S" />
      {{ node.name }}
    </ng-template>
  </fin-tree-menu>
</div>
