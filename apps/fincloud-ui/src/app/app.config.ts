import { ApplicationConfig } from '@angular/core';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideRouter } from '@angular/router';
import { DOCUMENT_CLASSIFICATION_LOADING_CONFIG } from '@fincloud/ui/document-classification';
import { FIN_CUSTOM_MESSAGES } from '@fincloud/ui/dropdown';
import {
  FIN_CURRENCY_MASK,
  FIN_DATE_MASK,
  FIN_DECIMAL_MASK,
  FIN_DEFAULT_REGION_ID,
  FIN_INTEGER_MASK,
  FIN_LOCALE_ID,
  FIN_MONTHS_MASK,
  FIN_PERCENTAGE_MASK,
  FIN_REGION_LOCALE_ID,
  LocaleId,
} from '@fincloud/ui/input';
import { appRoutes } from './app.routes';
import { CURRENCY_MASK_CONFIG } from './utils/currency-mask-config';
import { DECIMAL_MASK_CONFIG } from './utils/decimal-mask-config';
import { MASK_CONFIG_BASE } from './utils/mask-config-base';
import { PERCENTAGE_MASK_CONFIG } from './utils/percentage-mask-config';

// eslint-disable-next-line @fincloud/ns/no-export-constant
export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(appRoutes),
    provideAnimationsAsync(),
    {
      provide: FIN_CURRENCY_MASK,
      useValue: CURRENCY_MASK_CONFIG,
    },
    {
      provide: FIN_PERCENTAGE_MASK,
      useValue: PERCENTAGE_MASK_CONFIG,
    },
    {
      provide: FIN_DECIMAL_MASK,
      useValue: DECIMAL_MASK_CONFIG,
    },
    {
      provide: FIN_MONTHS_MASK,
      useValue: MASK_CONFIG_BASE,
    },
    {
      provide: FIN_INTEGER_MASK,
      useValue: MASK_CONFIG_BASE,
    },
    {
      provide: FIN_REGION_LOCALE_ID,
      useValue: LocaleId.DE,
    },
    {
      provide: FIN_CUSTOM_MESSAGES,
      useValue: {
        initialMessage: 'Enter text to search',
        noResultsMessage: 'No results found',
        initialSearchMessage:
          ' Enter 3 letters, 1 digit, or 1 special character to begin search.',
        searchPlaceholder: 'Search',
        result: 'result',
        results: 'results',
      },
    },
    {
      provide: FIN_DATE_MASK,
      useValue: {
        en: {
          dateFormat: 'dd/MM/yyyy',
        },
        de: {
          dateFormat: 'dd.MM.yyyy',
        },
      },
    },
    {
      provide: FIN_DEFAULT_REGION_ID,
      useValue: LocaleId.DE,
    },
    {
      provide: FIN_LOCALE_ID,
      useValue: LocaleId.DE,
    },
    {
      provide: DOCUMENT_CLASSIFICATION_LOADING_CONFIG,
      useValue: {
        timeoutDuration: 1000,
        timeoutMessage: 'AI is classifying {filename} location and naming',
      },
    },
  ],
};
