{"name": "@fincloud/ui", "homepage": "https://git.neo.loan/fincloud/lib-ui", "description": "Neoshare UI library kit", "version": "0.0.657", "peerDependencies": {"@angular/common": "~17.3.11", "@angular/core": "~17.3.11", "@angular/forms": "~17.3.11", "@nx/angular": "19.0.8", "tailwindcss-themer": "4.0.0", "@angular/material": "17.3.10", "rxjs": "7.8.1", "@ng-bootstrap/ng-bootstrap": "16.0.0", "@angular/platform-browser": "~17.3.11", "tailwindcss": "3.4.13", "@angular/cdk": "17.3.10", "@angular/router": "~17.3.11", "chart.js": "4.4.6", "ngx-scrollbar": "16.1.0", "angular-svg-icon": "17.0.0", "shelljs": "0.8.5", "ngx-currency": "17.0.0", "primeng": "17.18.15", "date-fns": "4.1.0", "ngx-mask": "19.0.6", "lodash-es": "4.17.21", "ngx-autosize": "2.0.4", "@angular/animations": "~17.3.11"}, "dependencies": {"@fincloud/utils": "0.0.657", "@fincloud/lib-ui-tokens": "1.0.35"}, "exports": {".": {"sass": "./styles/index.scss", "css": "./styles/base.css"}}, "sideEffects": false, "scripts": {"postinstall": "node ./postinstall.js"}}