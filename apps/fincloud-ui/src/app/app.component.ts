import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { FinIconModule } from '@fincloud/ui/icon';
import {
  FinTreeMenuModule,
  FinTreeNode,
  FinTreeNodeAppearance,
} from '@fincloud/ui/tree-menu';
import { FinSize } from '@fincloud/ui/types';
import { of } from 'rxjs';

@Component({
  standalone: true,
  imports: [CommonModule, FinIconModule, FinTreeMenuModule],
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinAppComponent implements OnInit {
  appearance = FinTreeNodeAppearance;
  size = FinSize;
  state = false;
  state$ = of(true);
  test = of(false);

  menuData = [
    {
      name: 'Item 1',
      children: [
        { name: 'Item 1.1', templateName: 'field' },
        { name: 'Item 1.2', templateName: 'field' },
        { name: 'Item 1.3', templateName: 'field' },
        { name: 'Item 1.4', templateName: 'field' },
        {
          name: 'Root Folder 1.5',
          templateName: 'folderRoot',
          children: [
            {
              name: 'Item 1.5.1',
              templateName: 'folder',
              children: [
                { name: 'Item 1.5.1.1', templateName: 'document' },
                { name: 'Item 1.5.1.2', templateName: 'document' },
                { name: 'Item 1.5.1.3', templateName: 'document' },
                { name: 'Item 1.5.1.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 1.5.2',
              templateName: 'folder',
              children: [
                { name: 'Item 1.5.2.1', templateName: 'document' },
                { name: 'Item 1.5.2.2', templateName: 'document' },
                { name: 'Item 1.5.2.3', templateName: 'document' },
                { name: 'Item 1.5.2.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 1.5.3',
              templateName: 'folder',
              children: [
                { name: 'Item 1.5.3.1', templateName: 'document' },
                { name: 'Item 1.5.3.2', templateName: 'document' },
                { name: 'Item 1.5.3.3', templateName: 'document' },
                { name: 'Item 1.5.3.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 1.5.4',
              templateName: 'folder',
              children: [
                { name: 'Item 1.5.4.1', templateName: 'document' },
                { name: 'Item 1.5.4.2', templateName: 'document' },
                { name: 'Item 1.5.4.3', templateName: 'document' },
                { name: 'Item 1.5.4.4', templateName: 'document' },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'Item 2',
      children: [
        { name: 'Item 2.1', templateName: 'field' },
        { name: 'Item 2.2', templateName: 'field' },
        { name: 'Item 2.3', templateName: 'field' },
        { name: 'Item 2.4', templateName: 'field' },
        {
          name: 'Root Folder 2.5',
          templateName: 'folderRoot',
          children: [
            {
              name: 'Item 2.5.1',
              templateName: 'folder',
              children: [
                { name: 'Item 2.5.1.1', templateName: 'document' },
                { name: 'Item 2.5.1.2', templateName: 'document' },
                { name: 'Item 2.5.1.3', templateName: 'document' },
                { name: 'Item 2.5.1.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 2.5.2',
              templateName: 'folder',
              children: [
                { name: 'Item 2.5.2.1', templateName: 'document' },
                { name: 'Item 2.5.2.2', templateName: 'document' },
                { name: 'Item 2.5.2.3', templateName: 'document' },
                { name: 'Item 2.5.2.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 2.5.3',
              templateName: 'folder',
              children: [
                { name: 'Item 2.5.3.1', templateName: 'document' },
                { name: 'Item 2.5.3.2', templateName: 'document' },
                { name: 'Item 2.5.3.3', templateName: 'document' },
                { name: 'Item 2.5.3.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 2.5.4',
              templateName: 'folder',
              children: [
                { name: 'Item 2.5.4.1', templateName: 'document' },
                { name: 'Item 2.5.4.2', templateName: 'document' },
                { name: 'Item 2.5.4.3', templateName: 'document' },
                { name: 'Item 2.5.4.4', templateName: 'document' },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'Item 3',
      children: [
        { name: 'Item 3.1', templateName: 'field' },
        { name: 'Item 3.2', templateName: 'field' },
        { name: 'Item 3.3', templateName: 'field' },
        { name: 'Item 3.4', templateName: 'field' },
        {
          name: 'Root Folder 3.5',
          templateName: 'folderRoot',
          children: [
            {
              name: 'Item 3.5.1',
              templateName: 'folder',
              children: [
                { name: 'Item 3.5.1.1', templateName: 'document' },
                { name: 'Item 3.5.1.2', templateName: 'document' },
                { name: 'Item 3.5.1.3', templateName: 'document' },
                { name: 'Item 3.5.1.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 3.5.2',
              templateName: 'folder',
              children: [
                { name: 'Item 3.5.2.1', templateName: 'document' },
                { name: 'Item 3.5.2.2', templateName: 'document' },
                { name: 'Item 3.5.2.3', templateName: 'document' },
                { name: 'Item 3.5.2.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 3.5.3',
              templateName: 'folder',
              children: [
                { name: 'Item 3.5.3.1', templateName: 'document' },
                { name: 'Item 3.5.3.2', templateName: 'document' },
                { name: 'Item 3.5.3.3', templateName: 'document' },
                { name: 'Item 3.5.3.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 3.5.4',
              templateName: 'folder',
              children: [
                { name: 'Item 3.5.4.1', templateName: 'document' },
                { name: 'Item 3.5.4.2', templateName: 'document' },
                { name: 'Item 3.5.4.3', templateName: 'document' },
                { name: 'Item 3.5.4.4', templateName: 'document' },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'Item 4',
      children: [
        { name: 'Item 4.1', templateName: 'field' },
        { name: 'Item 4.2', templateName: 'field' },
        { name: 'Item 4.3', templateName: 'field' },
        { name: 'Item 4.4', templateName: 'field' },
        { name: 'Item 4.5', templateName: 'field' },
        { name: 'Item 4.6', templateName: 'field' },
        { name: 'Item 4.7', templateName: 'field' },
        { name: 'Item 4.8', templateName: 'field' },
        {
          name: 'Root Folder 4.9',
          templateName: 'folderRoot',
          children: [
            {
              name: 'Item 4.5.1',
              templateName: 'folder',
              children: [
                { name: 'Item 4.5.1.1', templateName: 'document' },
                { name: 'Item 4.5.1.2', templateName: 'document' },
                { name: 'Item 4.5.1.3', templateName: 'document' },
                { name: 'Item 4.5.1.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 4.5.2',
              templateName: 'folder',
              children: [
                { name: 'Item 4.5.2.1', templateName: 'document' },
                { name: 'Item 4.5.2.2', templateName: 'document' },
                { name: 'Item 4.5.2.3', templateName: 'document' },
                { name: 'Item 4.5.2.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 4.5.3',
              templateName: 'folder',
              children: [
                { name: 'Item 4.5.3.1', templateName: 'document' },
                { name: 'Item 4.5.3.2', templateName: 'document' },
                { name: 'Item 4.5.3.3', templateName: 'document' },
                { name: 'Item 4.5.3.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 4.5.4',
              templateName: 'folder',
              children: [
                { name: 'Item 4.5.4.1', templateName: 'document' },
                { name: 'Item 4.5.4.2', templateName: 'document' },
                { name: 'Item 4.5.4.3', templateName: 'document' },
                { name: 'Item 4.5.4.4', templateName: 'document' },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'Item 5',
      children: [],
    },
    {
      name: 'Item 6',
      expanded: true,
      children: [
        { name: 'Item 6.1', templateName: 'field' },
        { name: 'Item 6.2', templateName: 'field' },
        { name: 'Item 6.3', templateName: 'field' },
        { name: 'Item 6.4', templateName: 'field' },
        {
          name: 'Root Folder 6.5',
          templateName: 'folderRoot',
          children: [
            {
              name: 'Item 6.5.1',
              templateName: 'folder',
              children: [
                { name: 'Item 6.5.1.1', templateName: 'document' },
                { name: 'Item 6.5.1.2', templateName: 'document' },
                { name: 'Item 6.5.1.3', templateName: 'document' },
                { name: 'Item 6.5.1.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 6.5.2',
              templateName: 'folder',
              children: [
                { name: 'Item 6.5.2.1', templateName: 'document' },
                { name: 'Item 6.5.2.2', templateName: 'document' },
                { name: 'Item 6.5.2.3', templateName: 'document' },
                { name: 'Item 6.5.2.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 6.5.3',
              templateName: 'folder',
              children: [
                { name: 'Item 6.5.3.1', templateName: 'document' },
                { name: 'Item 6.5.3.2', templateName: 'document' },
                { name: 'Item 6.5.3.3', templateName: 'document' },
                { name: 'Item 6.5.3.4', templateName: 'document' },
              ],
            },
            {
              name: 'Item 6.5.4',
              templateName: 'folder',
              children: [
                { name: 'Item 6.5.4.1', templateName: 'document' },
                { name: 'Item 6.5.4.2', templateName: 'document' },
                { name: 'Item 6.5.4.3', templateName: 'document' },
                { name: 'Item 6.5.4.4', templateName: 'document' },
              ],
            },
          ],
        },
      ],
    },
  ] as FinTreeNode<{
    name: string;
  }>[];

  ngOnInit() {
    console.log('onEdit');
  }

  onDrop(event: any) {
    console.log(event);
  }

  onNodeToggle(event: any) {
    console.log(event);
  }

  toggleEditMode() {
    console.log('HHHHHHH');
  }

  expand() {
    this.menuData[0].expanded = !this.menuData[0]?.expanded;
    // this.menuData = structuredClone(this.menuData);
    console.log(this.menuData);
  }

  onNodeClick = (event: MouseEvent) => {
    console.log('onNodeClick', event);
    this.toggleEditMode();
  };
}
